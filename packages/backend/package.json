{"name": "@arb-aio/backend", "version": "1.0.0", "description": "<PERSON><PERSON> backend for arb-aio", "main": "src/index.ts", "type": "module", "scripts": {"dev": "bun run --hot src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "test": "bun test"}, "dependencies": {"@hono/node-server": "^1.8.2", "@hono/zod-validator": "^0.7.2", "hono": "^3.12.8", "zod": "^4.0.14"}, "devDependencies": {"@types/bun": "latest", "typescript": "^5.3.3"}, "keywords": ["hono", "api", "backend"], "author": "", "license": "MIT"}