import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'

const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
  origin: ['http://localhost:3000'], // Next.js frontend URL
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
}))

// Schemas for validation
const UserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
})

const CreateUserSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
})

const ApiInfoSchema = z.object({
  message: z.string(),
  version: z.string(),
  timestamp: z.string(),
})

const HealthSchema = z.object({
  status: z.string(),
  uptime: z.number(),
  timestamp: z.string(),
})

// Mock data
let users = [
  { id: 1, name: 'John Doe', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
]

// Routes
app.get('/', (c) => {
  const data = {
    message: 'Welcome to ARB-AIO Backend API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  }
  return c.json(data)
})

app.get('/health', (c) => {
  const data = {
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  }
  return c.json(data)
})

// RPC API routes
const api = new Hono()

// Get all users
api.get('/users', (c) => {
  return c.json({ users })
})

// Get user by ID
api.get('/users/:id', (c) => {
  const id = parseInt(c.req.param('id'))
  const user = users.find(u => u.id === id)

  if (!user) {
    return c.json({ error: 'User not found' }, 404)
  }

  return c.json({ user })
})

// Create new user
api.post('/users', zValidator('json', CreateUserSchema), (c) => {
  const userData = c.req.valid('json')
  const newUser = {
    id: Date.now(),
    ...userData
  }

  users.push(newUser)

  return c.json({
    message: 'User created successfully',
    user: newUser
  }, 201)
})

// Update user
api.put('/users/:id', zValidator('json', CreateUserSchema), (c) => {
  const id = parseInt(c.req.param('id'))
  const userData = c.req.valid('json')
  const userIndex = users.findIndex(u => u.id === id)

  if (userIndex === -1) {
    return c.json({ error: 'User not found' }, 404)
  }

  users[userIndex] = { ...users[userIndex], ...userData }

  return c.json({
    message: 'User updated successfully',
    user: users[userIndex]
  })
})

// Delete user
api.delete('/users/:id', (c) => {
  const id = parseInt(c.req.param('id'))
  const userIndex = users.findIndex(u => u.id === id)

  if (userIndex === -1) {
    return c.json({ error: 'User not found' }, 404)
  }

  const deletedUser = users.splice(userIndex, 1)[0]

  return c.json({
    message: 'User deleted successfully',
    user: deletedUser
  })
})

app.route('/api', api)

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error(`${err}`)
  return c.json({ error: 'Internal Server Error' }, 500)
})

const port = process.env.PORT || 8000

console.log(`🚀 Server is running on http://localhost:${port}`)

// Export the app type for RPC client
export type AppType = typeof api

export default {
  port,
  fetch: app.fetch,
}
